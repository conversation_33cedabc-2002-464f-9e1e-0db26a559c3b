import requests
import json

BASE_URL = "https://ems-lgensol.singlex.com/maximo"
OBJECT_NAME = "ZLGAPIWO"
API_KEY = "ar06omth2ds6js8lt26god1nvhhdp8h60savkcla"

headers = {
    "Accept": "application/json",
    "apikey": API_KEY,
    "Content-Type": "application/json"
}

# oslc.where must be a single string
where_clause = 'siteid="UTIL.GM" and ownergroup!="GM.UT.U" and status="DRAFT" and istask=0 and zinfowoflag=0'
# where_clause = 'siteid="UTIL.GM" and ownergroup!="GM.UT.U" and status="CLOSE" and istask=0 and zinfowoflag=0'



# oslc.select defines which fields to return (including relationship expansion)
oslc_select = (
    "wogroup,"                              # Work Order Number
    "status,"                             # Work Order Status
    "description,"                        # Work Order Description
    "asset.description--assetdesc,"       # Asset Description
    "ownergroup.description--ownergroup," # Owner Group Description
    "schedstart,"                         # Scheduled Start Date
    "schedfinish,"                        # Scheduled Finish Date
    "actstart,"                           # Actual Start Date
    "actfinish,"                          # Actual Finish Date
    "changedate,"                         # Last Change Date
    "estdur"
)

# oslc_select = ("*")

params = {
    "oslc.where": where_clause,
    "lean": "1",
    "oslc.select": oslc_select
}

# Prepare the request to see the full URL
req = requests.Request(
    method="GET",
    url=f"{BASE_URL}/oslc/os/{OBJECT_NAME}",
    headers=headers,
    params=params
)
prepared = req.prepare()

# Print the full URL
print("Full URL:")
print(prepared.url)

# Send the request
session = requests.Session()
response = session.send(prepared, verify=False)

print("\nResponse status code:", response.status_code)
data = response.json()
# print(json.dumps(data, indent=2))
