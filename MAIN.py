import time
import datetime
import subprocess
from datetime import datetime, timezone

def run_scripts():
    now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"\n🕒 Running scripts at {now}")

    try:
        print("🚀 Running Get_Work_Order.py...")
        subprocess.run(["python", "Get_Work_Order.py"], check=True)
        print("✅ Get_Work_Order.py finished.")
    except subprocess.CalledProcessError as e:
        print(f"❌ Get_Work_Order.py failed: {e}")
        return

    try:
        print("📧 Running send_email.py...")
        subprocess.run(["python", "send_email.py"], check=True)
        print("✅ send_email.py finished.")
    except subprocess.CalledProcessError as e:
        print(f"❌ send_email.py failed: {e}")

    new_refresh_date = datetime.now(timezone.utc).replace(microsecond=0).isoformat()
    save_last_refresh_date(new_refresh_date)

def save_last_refresh_date(iso_str):
    with open("last_refresh_date.txt", "w") as f:
        f.write(iso_str)

def wait_until(target_hour):
    """Wait until the next target_hour (6 or 18)."""
    now = datetime.datetime.now()
    target = now.replace(hour=target_hour, minute=0, second=0, microsecond=0)
    if now >= target:
        target += datetime.timedelta(days=1)
    wait_seconds = (target - now).total_seconds()
    print(f"⏳ Waiting until {target.strftime('%Y-%m-%d %H:%M:%S')} ({int(wait_seconds)}s)")
    time.sleep(wait_seconds)

if __name__ == "__main__":
    run_scripts()
