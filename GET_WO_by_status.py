import requests
import json

BASE_URL = "https://ems-lgensol.singlex.com/maximo"
OBJECT_NAME = "oslcwodetail"
API_KEY = "ar06omth2ds6js8lt26god1nvhhdp8h60savkcla"

headers = {
    "Accept": "application/json",
    "apikey": API_KEY,
    "Content-Type": "application/json"
}

# oslc.where must be a single string
where_clause = 'spi_wm:wogroup="GM16260187" and spi_wm:haschildren=true'

# oslc.select defines which fields to return (including relationship expansion)
oslc_select = (
    "spi_wm:wogroup,"
    "spi:wostatus{spi_wm:parent,spi_wm:status,spi_wm:changeby,spi_wm:changedate},"
    "dcterms:identifier"
)

# oslc_select = ("*")


params = {
    "oslc.where": where_clause,
    "lean": "1",
    "oslc.select": oslc_select,
    "oslc.pageSize": "100"
}

# Prepare the request to see the full URL
req = requests.Request(
    method="GET",
    url=f"{BASE_URL}/oslc/os/{OBJECT_NAME}",
    headers=headers,
    params=params
)
prepared = req.prepare()

# Print the full URL
print("Full URL:")
print(prepared.url)

# Send the request
session = requests.Session()
response = session.send(prepared, verify=False)

print("\nResponse status code:", response.status_code)
data = response.json()
print(json.dumps(data, indent=2))
