import requests
import json
import sqlite3

BASE_URL = "https://ems-lgensol.singlex.com/maximo"
OBJECT_NAME = "ZLGAPIPERSON"
API_KEY = "ar06omth2ds6js8lt26god1nvhhdp8h60savkcla"

headers = {
    "Accept": "application/json",
    "apikey": API_KEY,
    "Content-Type": "application/json"
}

where_clause = 'locationsite="UTIL.GM"'


oslc_select = (
    "displayname, title, zempno"
)

params = {
    "oslc.where": where_clause,
    "lean": "1",
    "oslc.select": oslc_select
}

# Prepare the request to see the full URL
req = requests.Request(
    method="GET",
    url=f"{BASE_URL}/oslc/os/{OBJECT_NAME}",
    headers=headers,
    params=params
)
prepared = req.prepare()

print("Full URL:")
print(prepared.url)

session = requests.Session()
response = session.send(prepared, verify=False)

print("\nResponse status code:", response.status_code)
data = response.json()

import sqlite3

# --- Save to SQLite ---
conn = sqlite3.connect("data.db")
cur = conn.cursor()

# Create labor_name table if not exists
cur.execute("""
CREATE TABLE IF NOT EXISTS labor_name (
    displayname TEXT,
    title TEXT,
    zempno TEXT PRIMARY KEY
)
""")

# Remove rows where zempno is NULL before inserting new data
cur.execute("DELETE FROM labor_name WHERE zempno IS NULL")

# Extract records (Maximo can use either "member" or "rdfs:member")
records = data.get("member") or data.get("rdfs:member") or []

for rec in records:
    # Normalize into flat row
    displayname = rec.get("displayname")
    title = rec.get("title")
    zempno = rec.get("zempno")

    cur.execute("""
        INSERT OR REPLACE INTO labor_name
        (displayname, title, zempno)
        VALUES (?, ?, ?)
    """, (displayname, title, zempno))

conn.commit()
conn.close()

print(f"Inserted {len(records)} records into labor_name table.")
