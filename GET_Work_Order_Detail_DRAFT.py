import requests
import json
import sqlite3
from datetime import datetime
from zoneinfo import ZoneInfo  # native timezone support (Python 3.9+)

BASE_URL = "https://ems-lgensol.singlex.com/maximo"
OBJECT_NAME = "oslcwodetail"
API_KEY = "ar06omth2ds6js8lt26god1nvhhdp8h60savkcla"

headers = {
    "Accept": "application/json",
    "apikey": API_KEY,
    "Content-Type": "application/json"
}

# where_clause = (
#     'spi_wm:istask=0 and '
#     'spi_wm:woclass="WORKORDER" and '
#     'spi_wm:siteid="UTIL.GM" and '
#     'status="DRAFT" and '
#     'spi_wm:ownergroup!="GM.UT.U" and '
#     'spi_wm:haschildren=true'
# )
where_clause = (
    'spi_wm:istask=0 and '
    'spi_wm:woclass="WORKORDER" and '
    'spi_wm:siteid="UTIL.GM" and '
    'status="DRAFT" and '
    'spi_wm:ownergroup!="GM.UT.U"'
)

oslc_select = (
    "spi_wm:wogroup,"
    "ownergroup.description--trade,"
    "dcterms:title,"
    "spi_wm:zancestor,"
    "spi_wm:task{spi_wm:zlaborqty},"
    "spi_wm:zestdur2,"
    "spi_wm:schedstart,"
    "spi_wm:schedfinish,"
    "spi_wm:statusdate"
)

params = {
    "oslc.where": where_clause,
    "oslc.select": oslc_select,
    "lean": "1"
}

# === Send the request ===
req = requests.Request(
    method="GET",
    url=f"{BASE_URL}/oslc/os/{OBJECT_NAME}",
    headers=headers,
    params=params
)
prepared = req.prepare()

print("Full URL:")
print(prepared.url)

session = requests.Session()
response = session.send(prepared, verify=False)
print("\nResponse status code:", response.status_code)

data = response.json()

# === Function: Convert ISO UTC → Eastern local time ===
def format_iso_datetime_to_et(dt_str):
    """Convert UTC ISO string (e.g. 2025-06-08T11:39:00+00:00) → ET in 'YYYY-MM-DD HH:MM:SS'."""
    if not dt_str:
        return None
    try:
        # Parse as aware datetime (UTC)
        dt_utc = datetime.fromisoformat(dt_str.replace("Z", "+00:00"))
        # Convert to Eastern Time
        dt_et = dt_utc.astimezone(ZoneInfo("America/New_York"))
        return dt_et.strftime("%Y-%m-%d %H:%M:%S")
    except Exception as e:
        print(f"⚠️ Failed to parse datetime '{dt_str}': {e}")
        return dt_str  # fallback to original

# === Save to SQLite ===
conn = sqlite3.connect("data.db")
cur = conn.cursor()

cur.execute("DROP TABLE IF EXISTS Work_Order_DRAFT")
cur.execute("""
CREATE TABLE Work_Order_DRAFT (
    wonum TEXT PRIMARY KEY,
    trade TEXT,
    title TEXT,
    zancestor TEXT,
    Labor TEXT,
    duration TEXT,
    schedstart TEXT,
    schedfinish TEXT,
    statusdate TEXT
)
""")

records = data.get("member") or data.get("rdfs:member") or []

for rec in records:
    wonum = rec.get("wogroup")
    trade = rec.get("trade")
    title = rec.get("title")
    zancestor = rec.get("zancestor")
    duration = rec.get("zestdur2")

    # Convert times from UTC → ET
    schedstart = format_iso_datetime_to_et(rec.get("schedstart"))
    schedfinish = format_iso_datetime_to_et(rec.get("schedfinish"))
    statusdate = format_iso_datetime_to_et(rec.get("statusdate"))

    # Handle labor quantity
    tasks = rec.get("task", [])
    if isinstance(tasks, list) and tasks:
        zlaborqty = tasks[0].get("zlaborqty")
    else:
        zlaborqty = None

    cur.execute("""
        INSERT OR REPLACE INTO Work_Order_DRAFT
        (wonum, trade, title, zancestor, Labor, duration, schedstart, schedfinish, statusdate)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    """, (wonum, trade, title, zancestor, zlaborqty, duration, schedstart, schedfinish, statusdate))

conn.commit()
conn.close()

print(f"✅ Inserted {len(records)} work orders into Work_Order_DRAFT table (converted to Eastern Time).")
