import pandas as pd
from datetime import datetime, timedelta
import time
from collections import defaultdict
import sqlite3

def get_shift_letter(shift_start: datetime) -> str:
    """
    Determine which crew (A, B, C, D) is on shift at the given time.
    Shift pattern: 12-hour shifts, 6am-6pm (day) and 6pm-6am (night)

    Crew working hours:
    - A and C crews: 6:00 AM to 6:00 PM
    - B and D crews: 6:00 PM to 6:00 AM (next day)
    """
    # Reference date (January 1, 1900)
    ref_date = datetime(1900, 1, 1)

    # Calculate days since reference date
    days_since_ref_date_shift = (shift_start - ref_date).days
    days_since_ref_date_2024 = (datetime(2024, 1, 1) - ref_date).days

    # Shift pattern determination
    if 6 <= shift_start.hour < 18:
        # Day shifts (A or C) - 6 AM to 6 PM
        mod_value = (days_since_ref_date_shift - days_since_ref_date_2024 + 45292 + 18) % 6
        if mod_value < 3:
            return "A"
        else:
            return "C"
    else:
        # Night shifts (B or D) - 6 PM to 6 AM
        mod_value = (days_since_ref_date_shift - days_since_ref_date_2024 + 45292 - 12) % 6
        if mod_value < 3:
            return "B"
        else:
            return "D"

def validate_crew_assignment(crew: str, start_time: datetime, end_time: datetime) -> bool:
    """
    Validate that a crew assignment is within their working hours.

    Args:
        crew: Crew letter (A, B, C, or D)
        start_time: Work order start time
        end_time: Work order end time

    Returns:
        True if assignment is valid, False otherwise
    """
    start_hour = start_time.hour
    end_hour = end_time.hour

    # Handle end time that goes to next day
    if end_time.date() > start_time.date():
        end_hour += 24

    if crew in ['A', 'C']:
        # Day crews: 6 AM to 6 PM
        return 6 <= start_hour < 18 and end_hour <= 18
    elif crew in ['B', 'D']:
        # Night crews: 6 PM to 6 AM (next day)
        return start_hour >= 18 or start_hour < 6

    return False

def greedy_assignment(work_orders, employees, shift_hours=12, crew_letter=None, shift_start=None):
    """
    Fast greedy algorithm for work order assignment.
    Much faster than linear programming for large datasets.
    Now respects crew working hours.
    """
    # Sort work orders by priority (largest labor requirement first, then shortest duration)
    work_orders_sorted = sorted(work_orders, key=lambda x: (-x['labor_count'], x['duration']))

    # Track employee availability (remaining hours)
    emp_availability = {emp: shift_hours for emp in employees}
    emp_current_time = {emp: 0.0 for emp in employees}  # Track current time for each employee

    assignments = []
    assigned_wo_ids = set()

    for wo in work_orders_sorted:
        # Find employees with enough remaining time
        available_emps = [emp for emp in employees
                         if emp_availability[emp] >= wo['duration']]

        if len(available_emps) >= wo['labor_count']:
            # Sort by current time (earliest available first)
            available_emps.sort(key=lambda x: emp_current_time[x])

            # Assign the required number of employees
            assigned_emps = available_emps[:wo['labor_count']]

            # Find the latest start time among assigned employees
            start_time = max(emp_current_time[emp] for emp in assigned_emps)

            # Validate that this assignment respects crew working hours
            if crew_letter and shift_start:
                actual_start_time = shift_start + timedelta(hours=start_time)
                actual_end_time = actual_start_time + timedelta(hours=wo['duration'])

                if not validate_crew_assignment(crew_letter, actual_start_time, actual_end_time):
                    # Skip this work order if it would violate crew working hours
                    continue

            for emp in assigned_emps:
                assignments.append({
                    'work_order': wo['id'],
                    'employee': emp,
                    'start_time': start_time,
                    'duration': wo['duration'],
                    'labor_count': wo['labor_count']
                })

                # Update employee availability
                emp_availability[emp] -= wo['duration']
                emp_current_time[emp] = start_time + wo['duration']

            assigned_wo_ids.add(wo['id'])

    return assignments, assigned_wo_ids

def get_initial_shift_start(dt: datetime) -> datetime:
    """
    Round to the next appropriate shift start time.

    Shifts are:
      - 6:00 AM to 6:00 PM (Crews A and C)
      - 6:00 PM to 6:00 AM (Crews B and D)

    This function ensures we start scheduling from the next shift boundary
    to avoid assigning work to crews outside their working hours.
    """
    hour = dt.hour

    if hour < 6:
        # Before 6 AM → start from 6 AM of same day
        return dt.replace(hour=6, minute=0, second=0, microsecond=0)
    elif hour < 18:
        # Between 6 AM and 6 PM → start from 6 PM of same day
        return dt.replace(hour=18, minute=0, second=0, microsecond=0)
    else:
        # After 6 PM → start from 6 AM of next day
        return (dt + timedelta(days=1)).replace(hour=6, minute=0, second=0, microsecond=0)
    
# === Load Input from Excel ===
print("📂 Loading data...")
start_time = time.time()

SHIFT_HOURS = 12
SHIFT_DURATION = timedelta(hours=SHIFT_HOURS)
INITIAL_SHIFT_START = get_initial_shift_start(datetime.now())

print(f"🕐 Current time: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
print(f"🚀 Starting scheduling from: {INITIAL_SHIFT_START.strftime('%Y-%m-%d %H:%M')}")
print(f"👥 Initial crew: {get_shift_letter(INITIAL_SHIFT_START)}")

# Connect to SQLite database
conn = sqlite3.connect("data.db")

# Read the table into a DataFrame (now includes trade)
df_wo = pd.read_sql_query(
    "SELECT wonum, duration, Labor, trade, title, schedstart, schedfinish FROM Work_Order_DRAFT", conn
)


# Close the connection
conn.close()

# Build the work_orders list with trade
work_orders = [
    {
        'id': row['wonum'],
        'duration': float(row['duration']) / 60.0,
        'labor_count': int(row['Labor']) if row['Labor'] is not None else 1,  # Default to 1 if Labor is None
        'trade': row['trade'].strip() if row['trade'] else None,
        'title': row['title'].strip() if row['title'] else None,
        'schedstart': row['schedstart'].strip() if row['schedstart'] else None,
        'schedfinish': row['schedfinish'].strip() if row['schedfinish'] else None
    }
    for _, row in df_wo.iterrows()
]


# Load labor (with Crew + Trade)
df_labor = pd.read_excel("Labor.xlsx")
df_labor['Crew'] = df_labor['Crew'].astype(str).str.strip()
df_labor['Trade'] = df_labor['Trade'].astype(str).str.strip()
employees_all = df_labor[['Name', 'Crew', 'Trade']].dropna().to_dict('records')

print(f"📊 Loaded {len(work_orders)} work orders and {len(employees_all)} employees")
print(f"⏱️ Data loading took {time.time() - start_time:.2f} seconds")

# === Fast Multi-shift Scheduling ===
remaining_work_orders = work_orders.copy()
scheduled_rows = []
shift_index = 0
total_start_time = time.time()

while remaining_work_orders:
    shift_start_time = time.time()
    shift_start = INITIAL_SHIFT_START + shift_index * SHIFT_DURATION
    crew_letter = get_shift_letter(shift_start)

    # Calculate shift end time for validation
    shift_end = shift_start + SHIFT_DURATION

    print(f"🔍 Shift {shift_index + 1}: {shift_start.strftime('%Y-%m-%d %H:%M')} to {shift_end.strftime('%Y-%m-%d %H:%M')} -> Crew {crew_letter}")

    # Validate that this crew should be working during this time
    if crew_letter in ['A', 'C'] and not (6 <= shift_start.hour < 18):
        print(f"⚠️ ERROR: Day crew {crew_letter} scheduled outside 6AM-6PM hours!")
    elif crew_letter in ['B', 'D'] and not (shift_start.hour >= 18 or shift_start.hour < 6):
        print(f"⚠️ ERROR: Night crew {crew_letter} scheduled outside 6PM-6AM hours!")

    on_shift_employees = [e for e in employees_all
                          if e['Crew'] == crew_letter]
    # Use fast greedy assignment for each trade separately
    # --- Only schedule work orders due within the next 7 days ---
    current_time = shift_start
    planning_window = current_time + timedelta(days=7)

    eligible_wos = []
    future_wos = []

    for wo in remaining_work_orders:
        schedstart = wo.get('schedstart')
        if schedstart:
            try:
                # Parse datetime safely (allowing ISO or formatted)
                sched_dt = datetime.strptime(schedstart.split('.')[0], '%Y-%m-%d %H:%M:%S')
            except ValueError:
                try:
                    sched_dt = datetime.fromisoformat(schedstart.replace('Z', '+00:00'))
                except Exception:
                    sched_dt = None

            if sched_dt:
                if sched_dt <= planning_window:
                    eligible_wos.append(wo)
                else:
                    future_wos.append(wo)
            else:
                # If parsing fails, assume eligible
                eligible_wos.append(wo)
        else:
            # No schedstart → treat as eligible
            eligible_wos.append(wo)

    if not eligible_wos:
        print(f"⏭️ No eligible work orders for Shift {shift_index + 1} (all scheduled beyond 1 week).")
        shift_index += 1
        continue

    # Group only the eligible ones for assignment
    trade_groups = defaultdict(list)
    for wo in eligible_wos:
        trade_groups[wo['trade']].append(wo)

    for wo in remaining_work_orders:
        schedstart = wo.get('schedstart')
        if schedstart:
            try:
                sched_dt = datetime.strptime(schedstart, '%Y-%m-%d %H:%M:%S')
                if sched_dt <= current_time:
                    eligible_wos.append(wo)
                else:
                    future_wos.append(wo)
            except ValueError:
                # handle cases where schedstart is missing or in bad format
                eligible_wos.append(wo)
        else:
            eligible_wos.append(wo)

    if not eligible_wos:
        print(f"⏭️ No eligible work orders for Shift {shift_index + 1} (all scheduled in the future).")
        shift_index += 1
        continue

    # Group eligible WOs by trade
    trade_groups = defaultdict(list)
    for wo in eligible_wos:
        trade_groups[wo['trade']].append(wo)


    for trade, trade_wos in trade_groups.items():
        # Employees on this crew with correct trade
        trade_employees = [e['Name'] for e in on_shift_employees if e['Trade'] == trade]

        if not trade_employees:
            print(f"⚠️ No employees available for trade {trade} in crew {crew_letter}")
            continue

        assignments, assigned_wo_ids = greedy_assignment(trade_wos, trade_employees, SHIFT_HOURS, crew_letter, shift_start)
        
        for assignment in assignments:
            start_time = shift_start + timedelta(hours=assignment['start_time'])
            end_time = start_time + timedelta(hours=assignment['duration'])

            # Validate crew assignment
            if not validate_crew_assignment(crew_letter, start_time, end_time):
                print(f"⚠️ WARNING: Invalid crew assignment detected!")
                print(f"   Crew {crew_letter} assigned work from {start_time.strftime('%H:%M')} to {end_time.strftime('%H:%M')}")
                print(f"   Work Order: {assignment['work_order']}")
                continue

            def format_sched_time(t):
                """Convert ISO time like 2025-06-08T11:33:00+00:00 → 2025-06-08 11:33"""
                if not t:
                    return None
                try:
                    # Handle ISO 8601 timestamps with timezone info
                    return datetime.fromisoformat(t.replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M')
                except Exception:
                    return t  # return original if parsing fails

            # Build lookup dictionaries for title and schedule times
            title_lookup = {wo['id']: wo['title'] for wo in work_orders}
            schedstart_lookup = {wo['id']: wo['schedstart'] for wo in work_orders}
            schedfinish_lookup = {wo['id']: wo['schedfinish'] for wo in work_orders}


            scheduled_rows.append({
                'Work Order': assignment['work_order'],
                'Title': title_lookup.get(assignment['work_order']),
                'Assigned Employee': assignment['employee'],
                'Crew': crew_letter,
                'Shift': shift_index + 1,
                'Scheduled Start': schedstart_lookup.get(assignment['work_order']),
                'Scheduled Finish': schedfinish_lookup.get(assignment['work_order']),
                'Assigned Start Time': start_time.strftime('%Y-%m-%d %H:%M'),
                'Assigned End Time': end_time.strftime('%Y-%m-%d %H:%M'),
                'Duration (h)': round(assignment['duration'], 2),
                'Labor Required': assignment['labor_count'],
                'Trade': trade
            })


        # Remove completed WOs from pool
        remaining_work_orders = [wo for wo in remaining_work_orders if wo['id'] not in assigned_wo_ids]

    
    print(f"⏱️ Shift {shift_index + 1} processed in {time.time() - shift_start_time:.2f} seconds")
    shift_index += 1

# === Export Final Schedule ===
# Export to Excel
df_schedule = pd.DataFrame(scheduled_rows)

if not df_schedule.empty:
    df_schedule.sort_values(by=['Shift', 'Crew', 'Trade', 'Assigned Start Time', 'Assigned Employee'], inplace=True)
    
    # Print summary statistics
    print(f"\n📊 SCHEDULING SUMMARY:")
    print(f"Total work orders scheduled: {len(df_schedule['Work Order'].unique())}")
    print(f"Total shifts used: {df_schedule['Shift'].max()}")
    print(f"Employees utilized: {len(df_schedule['Assigned Employee'].unique())}")
    print(f"⏱️ Total processing time: {time.time() - total_start_time:.2f} seconds")
    
    # Show remaining unscheduled work orders
    if remaining_work_orders:
        print(f"\n⚠️ Unscheduled work orders: {len(remaining_work_orders)}")
        for wo in remaining_work_orders[:5]:  # Show only first 5
            print(f" - {wo['id']} (Duration: {wo['duration']}h, Labor: {wo['labor_count']})")
        if len(remaining_work_orders) > 5:
            print(f" ... and {len(remaining_work_orders) - 5} more")

# Export to Excel
df_schedule.to_excel("scheduled_multishift_output_fast.xlsx", index=False)

if df_schedule.empty:
    print("⚠️ No work orders were successfully assigned. Output file will be empty.")
else:
    print(f"\n✅ Schedule complete and saved to 'scheduled_multishift_output_fast.xlsx'")
    print(f"📁 File contains {len(df_schedule)} assignment records")

