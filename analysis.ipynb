import sqlite3

db_path = 'data.db'
conn = sqlite3.connect(db_path)
cur = conn.cursor()

cur.execute("DROP TABLE IF EXISTS Work_Order_DRAFT")

conn.commit()
conn.close()


import requests
import json
import sqlite3

BASE_URL = "https://ems-lgensol.singlex.com/maximo"
OBJECT_NAME = "ZLGAPIPERSON"
API_KEY = "ar06omth2ds6js8lt26god1nvhhdp8h60savkcla"

headers = {
    "Accept": "application/json",
    "apikey": API_KEY,
    "Content-Type": "application/json"
}

where_clause = 'locationsite="UTIL.GM"'


oslc_select = (
    "displayname, title, zempno"
)

params = {
    "oslc.where": where_clause,
    "lean": "1",
    "oslc.select": oslc_select
}

# Prepare the request to see the full URL
req = requests.Request(
    method="GET",
    url=f"{BASE_URL}/oslc/os/{OBJECT_NAME}",
    headers=headers,
    params=params
)
prepared = req.prepare()

print("Full URL:")
print(prepared.url)

session = requests.Session()
response = session.send(prepared, verify=False)

print("\nResponse status code:", response.status_code)
data = response.json()

import sqlite3

# --- Save to SQLite ---
conn = sqlite3.connect("data.db")
cur = conn.cursor()

# Create labor_name table if not exists
cur.execute("""
CREATE TABLE IF NOT EXISTS labor_name (
    displayname TEXT,
    title TEXT,
    zempno TEXT PRIMARY KEY
)
""")

# Remove rows where zempno is NULL before inserting new data
cur.execute("DELETE FROM labor_name WHERE zempno IS NULL")

# Extract records (Maximo can use either "member" or "rdfs:member")
records = data.get("member") or data.get("rdfs:member") or []

for rec in records:
    # Normalize into flat row
    displayname = rec.get("displayname")
    title = rec.get("title")
    zempno = rec.get("zempno")

    cur.execute("""
        INSERT OR REPLACE INTO labor_name
        (displayname, title, zempno)
        VALUES (?, ?, ?)
    """, (displayname, title, zempno))

conn.commit()
conn.close()

print(f"Inserted {len(records)} records into labor_name table.")


import requests
import json

BASE_URL = "https://ems-lgensol.singlex.com/maximo"
OBJECT_NAME = "ZLGAPIWO"
API_KEY = "ar06omth2ds6js8lt26god1nvhhdp8h60savkcla"

headers = {
    "Accept": "application/json",
    "apikey": API_KEY,
    "Content-Type": "application/json"
}

# oslc.where must be a single string
where_clause = 'wogroup="GM16232031"'
# where_clause = 'siteid="UTIL.GM" and ownergroup!="GM.UT.U" and status="CLOSE" and istask=0 and zinfowoflag=0'



# oslc.select defines which fields to return (including relationship expansion)
oslc_select = (
    "wogroup,"                              # Work Order Number
    "status,"                             # Work Order Status
    "description,"                        # Work Order Description
    "asset.description--assetdesc,"       # Asset Description
    "ownergroup.description--ownergroup," # Owner Group Description
    "schedstart,"                         # Scheduled Start Date
    "schedfinish,"                        # Scheduled Finish Date
    "actstart,"                           # Actual Start Date
    "actfinish,"                          # Actual Finish Date
    "changedate,"                         # Last Change Date
    "estdur"
)

oslc_select = ("*")

params = {
    "oslc.where": where_clause,
    "lean": "1",
    "oslc.select": oslc_select
}

# Prepare the request to see the full URL
req = requests.Request(
    method="GET",
    url=f"{BASE_URL}/oslc/os/{OBJECT_NAME}",
    headers=headers,
    params=params
)
prepared = req.prepare()

# Print the full URL
print("Full URL:")
print(prepared.url)

# Send the request
session = requests.Session()
response = session.send(prepared, verify=False)

print("\nResponse status code:", response.status_code)
data = response.json()
print(json.dumps(data, indent=2))


import requests
import json

# ---------------------------
# CONFIGURATION
# ---------------------------
API_KEY = "ar06omth2ds6js8lt26god1nvhhdp8h60savkcla"
WO_URL = "https://ems-lgensol.singlex.com/maximo/oslc/os/ZLGAPIWO/_VVRJTC5HTS9HTTE2MjMyMDMx"

headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "apikey": API_KEY,
    "x-method-override": "PATCH",  # override since PATCH not supported
    "patchtype": "MERGE"           # partial update
}

# ---------------------------
# PAYLOAD — fields to update
# ---------------------------
payload = {
    "schedstart": "2025-10-01T04:00:00+00:00",
    "schedfinish": "2025-10-01T06:30:00+00:00"
}

# ---------------------------
# SEND POST (acts as PATCH)
# ---------------------------
response = requests.post(WO_URL, headers=headers, data=json.dumps(payload), verify=False)

print("Status Code:", response.status_code)
print("Response:", response.text)
