{"cells": [{"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [], "source": ["import sqlite3\n", "\n", "db_path = 'data.db'\n", "conn = sqlite3.connect(db_path)\n", "cur = conn.cursor()\n", "\n", "cur.execute(\"DROP TABLE IF EXISTS Work_Order_DRAFT\")\n", "\n", "conn.commit()\n", "conn.close()\n"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Full URL:\n", "https://ems-lgensol.singlex.com/maximo/oslc/os/ZLGAPIPERSON?oslc.where=locationsite%3D%22UTIL.GM%22&lean=1&oslc.select=displayname%2C+title%2C+zempno\n", "\n", "Response status code: 200\n", "Inserted 136 records into labor_name table.\n"]}], "source": ["import requests\n", "import json\n", "import sqlite3\n", "\n", "BASE_URL = \"https://ems-lgensol.singlex.com/maximo\"\n", "OBJECT_NAME = \"ZLGAPIPERSON\"\n", "API_KEY = \"ar06omth2ds6js8lt26god1nvhhdp8h60savkcla\"\n", "\n", "headers = {\n", "    \"Accept\": \"application/json\",\n", "    \"apikey\": API_KEY,\n", "    \"Content-Type\": \"application/json\"\n", "}\n", "\n", "where_clause = 'locationsite=\"UTIL.GM\"'\n", "\n", "\n", "oslc_select = (\n", "    \"displayname, title, zempno\"\n", ")\n", "\n", "params = {\n", "    \"oslc.where\": where_clause,\n", "    \"lean\": \"1\",\n", "    \"oslc.select\": oslc_select\n", "}\n", "\n", "# Prepare the request to see the full URL\n", "req = requests.Request(\n", "    method=\"GET\",\n", "    url=f\"{BASE_URL}/oslc/os/{OBJECT_NAME}\",\n", "    headers=headers,\n", "    params=params\n", ")\n", "prepared = req.prepare()\n", "\n", "print(\"Full URL:\")\n", "print(prepared.url)\n", "\n", "session = requests.Session()\n", "response = session.send(prepared, verify=False)\n", "\n", "print(\"\\nResponse status code:\", response.status_code)\n", "data = response.json()\n", "\n", "import sqlite3\n", "\n", "# --- Save to SQLite ---\n", "conn = sqlite3.connect(\"data.db\")\n", "cur = conn.cursor()\n", "\n", "# Create labor_name table if not exists\n", "cur.execute(\"\"\"\n", "CREATE TABLE IF NOT EXISTS labor_name (\n", "    displayname TEXT,\n", "    title TEXT,\n", "    zempno TEXT PRIMARY KEY\n", ")\n", "\"\"\")\n", "\n", "# Remove rows where zemp<PERSON> is NULL before inserting new data\n", "cur.execute(\"DELETE FROM labor_name WHERE zempno IS NULL\")\n", "\n", "# Extract records (<PERSON><PERSON> can use either \"member\" or \"rdfs:member\")\n", "records = data.get(\"member\") or data.get(\"rdfs:member\") or []\n", "\n", "for rec in records:\n", "    # Normalize into flat row\n", "    displayname = rec.get(\"displayname\")\n", "    title = rec.get(\"title\")\n", "    zempno = rec.get(\"zempno\")\n", "\n", "    cur.execute(\"\"\"\n", "        INSERT OR REPLACE INTO labor_name\n", "        (displayname, title, zempno)\n", "        VALUES (?, ?, ?)\n", "    \"\"\", (displayname, title, zempno))\n", "\n", "conn.commit()\n", "conn.close()\n", "\n", "print(f\"Inserted {len(records)} records into labor_name table.\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.6"}}, "nbformat": 4, "nbformat_minor": 2}