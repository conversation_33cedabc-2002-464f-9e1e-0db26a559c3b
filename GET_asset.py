import requests
import json

BASE_URL = "https://ems-lgensol.singlex.com/maximo"
OBJECT_NAME = "ZLGAPIASSET"
API_KEY = "ar06omth2ds6js8lt26god1nvhhdp8h60savkcla"

headers = {
    "Accept": "application/json",
    "apikey": API_KEY,
    "Content-Type": "application/json"
}

where_clause = 'siteid="UTIL.GM" and status="ACTIVE" and zownerdepartment!="GM.UT.U"'


oslc_select = (
    "assetnum, description, zlevel, zownerdepartment.description--zownerdepartment"
)
# oslc_select = "*"

params = {
    "oslc.where": where_clause,
    "lean": "1",
    "oslc.select": oslc_select,
    # "oslc.pageSize": "10"
}

# Prepare the request to see the full URL
req = requests.Request(
    method="GET",
    url=f"{BASE_URL}/oslc/os/{OBJECT_NAME}",
    headers=headers,
    params=params
)
prepared = req.prepare()

print("Full URL:")
print(prepared.url)

session = requests.Session()
response = session.send(prepared, verify=False)

print("\nResponse status code:", response.status_code)
data = response.json()

import sqlite3

# --- Save to SQLite ---
conn = sqlite3.connect("data.db")
cur = conn.cursor()

# Create asset table if not exists
cur.execute("""
CREATE TABLE IF NOT EXISTS asset (
    zownerdepartment TEXT,
    description TEXT,
    assetnum TEXT PRIMARY KEY,
    zlevel TEXT     
)
""")

# Extract records (Maximo can use either "member" or "rdfs:member")
records = data.get("member") or data.get("rdfs:member") or []

for rec in records:
    # Normalize into flat row
    zownerdepartment = rec.get("zownerdepartment")
    description = rec.get("description")
    assetnum = rec.get("assetnum")
    zlevel = rec.get("zlevel")

    cur.execute("""
        INSERT OR REPLACE INTO asset
        (zownerdepartment, description, assetnum, zlevel)
        VALUES (?, ?, ?, ?)
    """, (zownerdepartment, description, assetnum, zlevel))

conn.commit()
conn.close()

print(f"Inserted {len(records)} records into asset table.")