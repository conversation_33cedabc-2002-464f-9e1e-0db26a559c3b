import requests
import json
import sqlite3
import pandas as pd
from urllib3.exceptions import InsecureRequestWarning

# Disable SSL warnings (since verify=False)
requests.packages.urllib3.disable_warnings(category=InsecureRequestWarning)

# -------------------- API CONFIG --------------------
BASE_URL = "https://ems-lgensol.singlex.com/maximo"
OBJECT_NAME = "ZLGAPIPM"
API_KEY = "ar06omth2ds6js8lt26god1nvhhdp8h60savkcla"

headers = {
    "Accept": "application/json",
    "apikey": API_KEY,
    "Content-Type": "application/json"
}

# Filter criteria
where_clause = 'siteid="UTIL.GM" and ownergroup!="GM.UT.U" and status="ACTIVE" and worktype!="DM" and usetargetdate=1'

# Selecting relevant fields
oslc_select = "pmnum,jpnum,description,usetargetdate"

params = {
    "oslc.where": where_clause,
    "lean": "1",
    "oslc.select": oslc_select
}

# -------------------- PREPARE REQUEST --------------------
req = requests.Request(
    method="GET",
    url=f"{BASE_URL}/oslc/os/{OBJECT_NAME}",
    headers=headers,
    params=params
)
prepared = req.prepare()

print("Full URL:")
print(prepared.url)

# -------------------- SEND REQUEST --------------------
session = requests.Session()
response = session.send(prepared, verify=False)

print("\nResponse status code:", response.status_code)

if response.status_code != 200:
    print("Error:", response.text)
    exit()

# -------------------- PARSE JSON --------------------
data = response.json()
records = data.get("member", [])

if not records:
    print("No data returned from API.")
    exit()

# Convert JSON list to pandas DataFrame
df = pd.json_normalize(records)

# Keep only useful fields
columns_to_keep = ["pmnum", "jpnum", "description", "usetargetdate"]
df = df[columns_to_keep]

# Rename columns for clarity
df.rename(columns={
    "pmnum": "PMNUM",
    "jpnum": "JPNUM",
    "description": "DESCRIPTION",
    "usetargetdate": "USETARGETDATE"
}, inplace=True)

print("\nPreview of data:")
print(df.head())

# -------------------- SAVE TO SQLITE --------------------
db_path = "data.db"
table_name = "PM Association"

conn = sqlite3.connect(db_path)
cursor = conn.cursor()

# Create table if not exists
cursor.execute(f"""
CREATE TABLE IF NOT EXISTS '{table_name}' (
    PMNUM TEXT,
    JPNUM TEXT,
    DESCRIPTION TEXT,
    USETARGETDATE TEXT
);
""")

# Optional: clear previous data
cursor.execute(f"DELETE FROM '{table_name}';")
conn.commit()

# Insert the new data
df.to_sql(table_name, conn, if_exists="append", index=False)

conn.close()

print(f"\n✅ Table '{table_name}' created and data saved to '{db_path}'.")
