import pandas as pd

# Read the Excel file
df = pd.read_excel('scheduled_multishift_output_fast.xlsx')

print(f"Total records: {len(df)}")
print(f"Unique crews: {df['Crew'].unique()}")
print()

# Check sample assignments for each crew
for crew in ['A', 'B', 'C', 'D']:
    crew_data = df[df['Crew'] == crew]
    if len(crew_data) > 0:
        print(f"Crew {crew} sample (first 3 records):")
        sample = crew_data[['Work Order', 'Start Time', 'End Time']].head(3)
        for _, row in sample.iterrows():
            print(f"  {row['Work Order']} | {row['Start Time']} to {row['End Time']}")
        print()

# Validate crew assignments
print("Validating crew assignments...")
invalid_count = 0

for _, row in df.iterrows():
    crew = row['Crew']
    start_time = pd.to_datetime(row['Start Time'])
    end_time = pd.to_datetime(row['End Time'])
    
    start_hour = start_time.hour
    end_hour = end_time.hour
    
    # Handle end time that goes to next day
    if end_time.date() > start_time.date():
        end_hour += 24
    
    if crew in ['A', 'C']:
        # Day crews: 6 AM to 6 PM
        if not (6 <= start_hour < 18 and end_hour <= 18):
            print(f"INVALID: Crew {crew} assigned from {start_time.strftime('%H:%M')} to {end_time.strftime('%H:%M')} - Work Order: {row['Work Order']}")
            invalid_count += 1
    elif crew in ['B', 'D']:
        # Night crews: 6 PM to 6 AM
        if not (start_hour >= 18 or start_hour < 6):
            print(f"INVALID: Crew {crew} assigned from {start_time.strftime('%H:%M')} to {end_time.strftime('%H:%M')} - Work Order: {row['Work Order']}")
            invalid_count += 1

if invalid_count == 0:
    print("✅ All assignments are valid! No crews assigned outside their working hours.")
else:
    print(f"❌ Found {invalid_count} invalid assignments.")

print(f"\nSummary:")
print(f"- Total work orders scheduled: {len(df['Work Order'].unique())}")
print(f"- Total assignment records: {len(df)}")
print(f"- Crews used: {sorted(df['Crew'].unique())}")
