import requests
import json
import urllib3
import os
import sqlite3
from datetime import datetime, timezone
from zoneinfo import ZoneInfo

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def load_last_refresh_date():
    filename = "last_refresh_date.txt"
    if os.path.exists(filename):
        with open(filename, "r") as f:
            return f.read().strip()

BASE_URL = "https://ems-lgensol.singlex.com/maximo"
OBJECT_NAME = "ZLGAPIWO"
API_KEY = "ar06omth2ds6js8lt26god1nvhhdp8h60savkcla"

headers = {
    "Accept": "application/json",
    "apikey": API_KEY,
    "Content-Type": "application/json"
}

# oslc.where must be a single string
LAST_REFRESH_DATE = load_last_refresh_date()

where_clause = (
    'siteid="UTIL.GM" and '
    'ownergroup!="GM.UT.U" and '
    'istask=0 and '
    'status!="DRAFT" and '
    'status!="INPRG" and '
    'status!="OPCOMP" and '
    'status!="CAN" and '
    f'changedate>"{LAST_REFRESH_DATE}"'
)

# oslc.select defines which fields to return (including relationship expansion)
oslc_select = (
    "wonum,"                              # Work Order Number
    "status,"                             # Work Order Status
    "description,"                        # Work Order Description
    "asset.description--assetdesc,"       # Asset Description
    "ownergroup.description--ownergroup," # Owner Group Description
    "schedstart,"                         # Scheduled Start Date
    "schedfinish,"                        # Scheduled Finish Date
    "actstart,"                           # Actual Start Date
    "actfinish,"                          # Actual Finish Date
    "changedate"                          # Last Change Date
)

params = {
    "oslc.where": where_clause,
    "lean": "1",
    "oslc.select": oslc_select
}

# Prepare the request to see the full URL
req = requests.Request(
    method="GET",
    url=f"{BASE_URL}/oslc/os/{OBJECT_NAME}",
    headers=headers,
    params=params
)
prepared = req.prepare()

print("Full URL:")
print(prepared.url)

session = requests.Session()
response = session.send(prepared, verify=False)

print("\nResponse status code:", response.status_code)
data = response.json()

# --- Save to SQLite ---
conn = sqlite3.connect("data.db")
cur = conn.cursor()

# Create Work_Order_COMP table if not exists
cur.execute("""
CREATE TABLE IF NOT EXISTS Work_Order_COMP (
    wonum TEXT PRIMARY KEY,
    status TEXT,
    description TEXT,
    assetdesc TEXT,
    ownergroup TEXT,
    schedstart TEXT,
    schedfinish TEXT,
    actstart TEXT,
    actfinish TEXT,
    changedate TEXT
)
""")

# Extract records (Maximo can use either "member" or "rdfs:member")
records = data.get("member") or data.get("rdfs:member") or []

for rec in records:
    # Normalize into flat row
    wonum = rec.get("wonum")
    status = rec.get("status")
    description = rec.get("description")
    assetdesc = rec.get("assetdesc")
    ownergroup = rec.get("ownergroup")
    schedstart = rec.get("schedstart")
    schedfinish = rec.get("schedfinish")
    actstart = rec.get("actstart")
    actfinish = rec.get("actfinish")
    changedate = rec.get("changedate")

    cur.execute("""
    INSERT OR REPLACE INTO Work_Order_COMP
    (wonum, status, description, assetdesc, ownergroup, schedstart, schedfinish, actstart, actfinish, changedate)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """, (wonum, status, description, assetdesc, ownergroup, schedstart, schedfinish, actstart, actfinish, changedate))

conn.commit()
conn.close()

print(f"Inserted {len(records)} work orders into Work_Order_COMP table.")